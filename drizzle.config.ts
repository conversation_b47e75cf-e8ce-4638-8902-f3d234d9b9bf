import { defineConfig } from "drizzle-kit";
import { config } from "dotenv";

// Load environment variables
config();

// Debug: Check if environment variables are loaded
console.log("TURSO_DATABASE_URL:", process.env.TURSO_DATABASE_URL);
console.log("TURSO_AUTH_TOKEN:", process.env.TURSO_AUTH_TOKEN ? "***TOKEN_PRESENT***" : "***TOKEN_MISSING***");

export default defineConfig({
  dialect: "sqlite",
  schema: "./src/lib/db/schema.ts",
  out: "./drizzle",
  dbCredentials: {
    url: process.env.TURSO_DATABASE_URL!,
    authToken: process.env.TURSO_AUTH_TOKEN!,
  },
});
