@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 206 89% 94.1%;
    --foreground: 210 20% 22.2%;
    --card: 0 0% 100%;
    --card-foreground: 210 20% 22.2%;
    --popover: 0 0% 100%;
    --popover-foreground: 210 20% 22.2%;
    --primary: 207 88% 68%;
    --primary-foreground: 0 0% 100%;
    --secondary: 123 45% 63.9%;
    --secondary-foreground: 0 0% 100%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 123 45% 64%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 207 88% 68%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 210 20% 12%;
    --foreground: 0 0% 98%;
    --card: 210 20% 15%;
    --card-foreground: 0 0% 98%;
    --popover: 210 20% 15%;
    --popover-foreground: 0 0% 98%;
    --primary: 207 88% 68%;
    --primary-foreground: 210 40% 98%;
    --secondary: 123 45% 63.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 210 20% 20%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 123 45% 63.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 210 20% 25%;
    --input: 210 20% 25%;
    --ring: 207 88% 75%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
