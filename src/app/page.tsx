
'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { PlusCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { AppHeader } from '@/components/header';
import { TripCard } from '@/components/trip-card';
import { CreateTripDialog } from '@/components/create-trip-dialog';
import { Skeleton } from '@/components/ui/skeleton';
import type { Trip } from '@/lib/types';
import { getTrips } from './actions';
import { usePathname } from 'next/navigation';

export default function Home() {
  const [trips, setTrips] = useState<Trip[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [isCreateTripOpen, setCreateTripOpen] = useState(false);

  // The 'key' state is used to force a re-render and data refetch when navigating
  // back to the page after a trip has been created or modified.
  const [key, setKey] = useState(Date.now());
  const pathname = usePathname();

  useEffect(() => {
    async function fetchTrips() {
      try {
        setIsLoading(true);
        const result = await getTrips();
        // The data from the database needs to have its date fields converted to Date objects
        const formattedTrips = result.map(trip => ({
          ...trip,
          startDate: new Date(trip.startDate),
          endDate: new Date(trip.endDate),
          itinerary: trip.itinerary.map(day => ({
            ...day,
            date: new Date(day.date),
            activities: day.activities.map(activity => ({...activity}))
          }))
        })) as Trip[];
        setTrips(formattedTrips);
      } catch (err) {
        setError(err as Error);
      } finally {
        setIsLoading(false);
      }
    }
    fetchTrips();
  }, [key]); // Depend on 'key' to refetch when it changes

  // This effect updates the key when the path changes, which can signal a navigation event.
  useEffect(() => {
    setKey(Date.now());
  }, [pathname]);

  if (error) {
    return (
      <div className="flex flex-col min-h-screen">
        <AppHeader />
        <main className="flex-1 container mx-auto p-4 md:p-8 flex items-center justify-center">
            <div className="text-center p-8 border-2 border-dashed rounded-lg border-destructive/50 bg-destructive/5">
                <h2 className="text-xl font-semibold text-destructive">Error Loading Trips</h2>
                <p className="text-destructive/90 mt-2">
                    There was a problem fetching your trip data. This may be due to a database schema mismatch.
                </p>
                <p className="text-muted-foreground mt-4 text-sm">
                    You can try to resolve this by clearing all existing data. <br/> 
                    Please note that this action is irreversible and will delete all trips.
                </p>
                <Button variant="destructive" className="mt-4" asChild>
                    <Link href="/admin">
                      Go to Admin Page to Clear Data
                    </Link>
                </Button>
            </div>
        </main>
      </div>
    );
  }
  
  return (
    <div className="flex flex-col min-h-screen">
      <AppHeader>
        <Button onClick={() => setCreateTripOpen(true)}>
          <PlusCircle />
          Create New Trip
        </Button>
      </AppHeader>
      <main className="flex-1 container mx-auto p-4 md:p-8">
        <CreateTripDialog
          isOpen={isCreateTripOpen}
          onOpenChange={setCreateTripOpen}
          onTripCreated={() => setKey(Date.now())} // Add a callback to refresh data
        />
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold font-headline tracking-tight">Your Trips</h1>
          <p className="text-muted-foreground mt-2">
            Here's a list of all your upcoming adventures.
          </p>
        </div>

        {isLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="flex flex-col space-y-3">
                <Skeleton className="h-[225px] w-full rounded-xl" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </div>
            ))}
          </div>
        ) : trips.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {trips.map((trip) => (
              <Link href={`/trip/${trip.id}`} key={trip.id} passHref>
                <TripCard trip={trip} />
              </Link>
            ))}
          </div>
        ) : (
          <div className="text-center py-20 border-2 border-dashed rounded-lg">
            <h2 className="text-xl font-semibold">Welcome to WanderEase!</h2>
            <p className="text-muted-foreground mt-2">
              Start planning your next adventure by creating a new trip.
            </p>
            <Button className="mt-4" onClick={() => setCreateTripOpen(true)}>
              <PlusCircle />
              Create Your First Trip
            </Button>
          </div>
        )}
      </main>
    </div>
  );
}
