
'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { AppHeader } from '@/components/header';
import { clearAllData } from '@/app/actions';
import { useToast } from '@/hooks/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Trash2 } from 'lucide-react';

export default function AdminPage() {
  const [isPending, setIsPending] = useState(false);
  const { toast } = useToast();

  const handleClearData = async () => {
    setIsPending(true);
    const result = await clearAllData();
    if (result.success) {
      toast({
        title: "Database Cleared",
        description: "All trip data has been successfully deleted.",
      });
    } else {
      toast({
        variant: "destructive",
        title: "Error",
        description: result.error,
      });
    }
    setIsPending(false);
  };

  return (
    <div className="flex flex-col min-h-screen">
      <AppHeader />
      <main className="flex-1 container mx-auto p-4 md:p-8 flex items-center justify-center">
        <div className="w-full max-w-md p-8 space-y-6 bg-card rounded-lg shadow-lg">
            <div className="text-center">
                <h1 className="text-2xl font-bold">Admin Control Panel</h1>
                <p className="text-muted-foreground">Use these tools with caution.</p>
            </div>
            
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" className="w-full" disabled={isPending}>
                    <Trash2 className="mr-2 h-4 w-4" />
                    {isPending ? 'Clearing Data...' : 'Clear All Trip Data'}
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                  <AlertDialogHeader>
                      <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                      <AlertDialogDescription>
                      This action cannot be undone. This will permanently delete all trips, itinerary days, and activities from the database.
                      </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction onClick={handleClearData} disabled={isPending} className="bg-destructive hover:bg-destructive/90">
                          {isPending ? 'Deleting...' : 'Yes, delete everything'}
                      </AlertDialogAction>
                  </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
            <p className="text-xs text-center text-muted-foreground pt-4">
                This will wipe all data from the trips, itinerary_days, and activities tables to ensure the application starts with a clean slate.
            </p>
        </div>
      </main>
    </div>
  );
}
