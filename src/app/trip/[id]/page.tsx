
'use client';

import { useParams, notFound } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Share2, ArrowLeft, RefreshCw } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { format } from 'date-fns';
import { ItineraryTimeline } from '@/components/itinerary-timeline';
import { ActivitySuggestions } from '@/components/activity-suggestions';
import { FullItineraryPlanner } from '@/components/full-itinerary-planner';
import { useToast } from '@/hooks/use-toast';
import { Skeleton } from '@/components/ui/skeleton';
import { AppHeader } from '@/components/header';
import { getTrip } from '@/app/actions';
import { useState, useEffect, useCallback, useTransition } from 'react';
import type { Trip } from '@/lib/types';


export default function TripPage() {
  const params = useParams();
  const { toast } = useToast();
  
  const tripId = typeof params.id === 'string' ? params.id : '';

  const [trip, setTrip] = useState<Trip | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [isRefreshing, startRefreshTransition] = useTransition();

  const fetchTrip = useCallback(async () => {
    if (!tripId) return;

    try {
      const result = await getTrip(tripId);

      if (result) {
          const formattedTrip = {
              ...result,
              startDate: new Date(result.startDate),
              endDate: new Date(result.endDate),
              itinerary: result.itinerary.map(day => ({
                  ...day,
                  date: new Date(day.date),
                  activities: day.activities.map(activity => ({...activity}))
              }))
          } as Trip;
          setTrip(formattedTrip);
      } else {
          setTrip(null);
      }
    } catch (err) {
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  }, [tripId]);

  useEffect(() => {
    setIsLoading(true);
    fetchTrip();
  }, [fetchTrip]);

  const refreshData = () => {
    startRefreshTransition(() => {
      fetchTrip();
    });
  };

  if (error) {
    return <div className="p-8">Error: {error.message}</div>
  }

  if (isLoading) {
    return (
      <>
        <AppHeader />
        <div className="p-4 md:p-8 space-y-8">
            <div className="flex justify-between">
                <Skeleton className="h-10 w-36" />
                <Skeleton className="h-10 w-28" />
            </div>
            <Skeleton className="h-64 md:h-96 w-full" />
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div className="lg:col-span-2 space-y-4">
                    <Skeleton className="h-8 w-1/3" />
                    <Skeleton className="h-48 w-full" />
                    <Skeleton className="h-48 w-full" />
                </div>
                <div className="space-y-4">
                    <Skeleton className="h-64 w-full" />
                    <Skeleton className="h-64 w-full" />
                </div>
            </div>
        </div>
      </>
    );
  }

  if (!trip) {
    notFound();
  }

  const handleShare = () => {
    const shareUrl = `${window.location.origin}/share/${trip.id}`;
    navigator.clipboard.writeText(shareUrl);
    toast({
      title: "Link Copied!",
      description: "Your trip itinerary link is ready to be shared.",
    });
  };

  return (
    <>
      <AppHeader>
        <div className="flex items-center gap-2">
            <Button variant="outline" asChild>
                <Link href="/"><ArrowLeft /> Back to Trips</Link>
            </Button>
            <Button onClick={handleShare}>
              <Share2 />
              Share
            </Button>
        </div>
      </AppHeader>
      <main className="container mx-auto p-4 md:p-8">
        <div className="relative h-64 md:h-96 rounded-2xl overflow-hidden mb-8 shadow-lg">
            <Image
                src={trip.imageUrl}
                alt={trip.destination}
                fill
                className="object-cover"
                data-ai-hint={trip.imageHint}
                priority
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
            <div className="absolute bottom-0 left-0 p-8">
                <h1 className="text-4xl md:text-6xl font-bold font-headline text-white">{trip.destination}</h1>
                <p className="text-lg md:text-xl text-white/90 mt-2">
                    {format(trip.startDate, 'MMMM d, yyyy')} - {format(trip.endDate, 'MMMM d, yyyy')}
                </p>
            </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
                <div className="flex justify-between items-center mb-4">
                    <h2 className="text-3xl font-bold font-headline tracking-tight">Your Itinerary</h2>
                    <Button variant="ghost" size="sm" onClick={refreshData} disabled={isRefreshing}>
                        <RefreshCw className={isRefreshing ? "animate-spin" : ""} />
                        {isRefreshing ? "Refreshing..." : "Refresh"}
                    </Button>
                </div>
                <ItineraryTimeline tripId={trip.id} itinerary={trip.itinerary} onDataChange={refreshData} />
            </div>
            <aside className="lg:col-span-1 space-y-8">
                <div className="sticky top-24">
                  <ActivitySuggestions tripId={trip.id} location={trip.destination} itinerary={trip.itinerary} onDataChange={refreshData} />
                  <div className="mt-8">
                    <FullItineraryPlanner trip={trip} onDataChange={refreshData} />
                  </div>
                </div>
            </aside>
        </div>

      </main>
    </>
  );
}
