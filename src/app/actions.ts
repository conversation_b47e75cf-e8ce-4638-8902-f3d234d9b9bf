
'use server';

import { generateActivitySuggestions } from '@/ai/flows/generate-activity-suggestions';
import type { GenerateActivitySuggestionsInput } from '@/ai/flows/generate-activity-suggestions';
import { generateFullItinerary } from '@/ai/flows/generate-full-itinerary';
import type { GenerateFullItineraryInput } from '@/ai/flows/generate-full-itinerary';
import { z } from 'zod';
import { db } from '@/lib/db';
import { activities, itineraryDays, trips } from '@/lib/db/schema';
import { getDaysBetweenDates } from '@/lib/utils';
import { revalidatePath } from 'next/cache';
import { desc, eq, inArray } from 'drizzle-orm';
import type { ItineraryDay, Activity } from '@/lib/types';

const inputSchema = z.object({
  location: z.string(),
  itinerary: z.any(),
});

export async function generateActivitySuggestionsAction(input: GenerateActivitySuggestionsInput) {
  const parsedInput = inputSchema.safeParse(input);

  if (!parsedInput.success) {
    return { error: 'Invalid input.' };
  }

  try {
    const result = await generateActivitySuggestions(parsedInput.data);
    return { suggestions: result.suggestions };
  } catch (error) {
    console.error('Error generating activity suggestions:', error);
    return { error: 'Could not generate suggestions at this time. Please try again later.' };
  }
}

export async function generateFullItineraryAction(input: GenerateFullItineraryInput) {
  try {
    const result = await generateFullItinerary(input);
    return { plan: result };
  } catch (error)
{
    console.error('Error generating full itinerary:', error);
    return { error: 'Could not generate a full itinerary at this time. Please try again later.' };
  }
}

export async function createTrip(data: { destination: string, startDate: Date, endDate: Date }) {
    const tripId = `${data.destination.toLowerCase().replace(/[^a-z0-9]/g, '-')}-${Date.now()}`;
    
    await db.transaction(async (tx) => {
        await tx.insert(trips).values({
            id: tripId,
            destination: data.destination,
            startDate: data.startDate,
            endDate: data.endDate,
            imageUrl: `https://picsum.photos/seed/${tripId}/800/600`,
            imageHint: data.destination.split(',')[0].trim(),
        });

        const dates = getDaysBetweenDates(data.startDate, data.endDate);
        const days = dates.map(date => ({
            id: `day-${date.getTime()}-${tripId}`,
            date,
            tripId,
        }));

        if(days.length > 0) {
            await tx.insert(itineraryDays).values(days);
        }
    });

    revalidatePath('/');
    return { id: tripId };
}


export async function addActivityAction(tripId: string, dayId: string, data: { time: string, name: string, description: string, location?: string }) {
    await db.insert(activities).values({
        id: `activity-${Date.now()}`,
        name: data.name,
        description: data.description,
        time: data.time,
        location: data.location,
        dayId: dayId,
    });
    revalidatePath(`/trip/${tripId}`);
    revalidatePath(`/share/${tripId}`);
}

export async function updateActivityAction(tripId: string, activityId: string, data: { time: string, name: string, description: string, location?: string }) {
    await db.update(activities)
        .set({
            time: data.time,
            name: data.name,
            description: data.description,
            location: data.location
        })
        .where(eq(activities.id, activityId));

    revalidatePath(`/trip/${tripId}`);
    revalidatePath(`/share/${tripId}`);
}

export async function deleteActivityAction(tripId: string, activityId: string) {
    await db.delete(activities).where(eq(activities.id, activityId));
    revalidatePath(`/trip/${tripId}`);
    revalidatePath(`/share/${tripId}`);
}


export async function addSuggestedActivitiesToAction(tripId: string, dayId: string, suggestions: { name: string; description: string; time: string; location?: string }[]) {
    const newActivities = suggestions.map(suggestion => ({
        id: `activity-${suggestion.time}-${Date.now()}-${Math.random()}`,
        dayId: dayId,
        time: suggestion.time,
        name: suggestion.name,
        description: suggestion.description,
        location: suggestion.location,
    }));

    if (newActivities.length > 0) {
        await db.insert(activities).values(newActivities);
    }
    revalidatePath(`/trip/${tripId}`);
    revalidatePath(`/share/${tripId}`);
}

export async function getTrips() {
    const result = await db.query.trips.findMany({
        orderBy: desc(trips.startDate),
        with: {
            itinerary: {
                with: {
                    activities: {
                        orderBy: (activities, { asc }) => [asc(activities.time)],
                    }
                }
            }
        }
    });
    return result;
}

export async function getTrip(tripId: string) {
    if (!tripId) {
        return null;
    }
    try {
        const result = await db.query.trips.findFirst({
            where: eq(trips.id, tripId),
            with: {
                itinerary: {
                    orderBy: (itineraryDays, { asc }) => [asc(itineraryDays.date)],
                    with: {
                        activities: {
                            orderBy: (activities, { asc }) => [asc(activities.time)],
                        }
                    }
                }
            }
        });
        return result;
    } catch (error) {
        console.error("Error fetching trip:", error);
        return null;
    }
}

export async function clearAllData() {
    try {
        await db.transaction(async (tx) => {
            await tx.delete(activities);
            await tx.delete(itineraryDays);
            await tx.delete(trips);
        });
        revalidatePath('/');
        return { success: true };
    } catch (error) {
        console.error("Error clearing data:", error);
        return { success: false, error: 'Could not clear data.' };
    }
}
