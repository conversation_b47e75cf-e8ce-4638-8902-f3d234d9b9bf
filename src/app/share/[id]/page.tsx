'use client';

import { useEffect, useState } from 'react';
import { useParams, notFound, usePathname } from 'next/navigation';
import { AppHeader } from '@/components/header';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Terminal, Eye, CalendarDays, MapPin, Clock } from 'lucide-react';
import Image from 'next/image';
import { format } from 'date-fns';
import type { Trip } from '@/lib/types';
import { Skeleton } from '@/components/ui/skeleton';
import { getTrip } from '@/app/actions';


function ReadOnlyItineraryTimeline({ itinerary }: { itinerary: Trip['itinerary'] }) {
    if (!itinerary || itinerary.length === 0) {
        return (
            <div className="text-center py-20 border-2 border-dashed rounded-lg">
                <h2 className="text-xl font-semibold">No itinerary details available.</h2>
                <p className="text-muted-foreground mt-2">
                    This trip doesn't have any planned days or activities yet.
                </p>
            </div>
        );
    }
    return (
      <div className="space-y-12">
        {itinerary.map((day, dayIndex) => (
          <div key={day.id} className="relative pl-10 md:pl-12">
            <div className="absolute left-4 top-2 h-full w-0.5 bg-border -translate-x-1/2"></div>
            <div className="absolute left-4 top-2 w-4 h-4 rounded-full bg-primary ring-4 ring-background -translate-x-1/2"></div>
            
            <div className="mb-4">
                <h3 className="text-xl font-bold font-headline text-primary">Day {dayIndex + 1}</h3>
                <p className="text-muted-foreground flex items-center gap-2">
                    <CalendarDays className="w-4 h-4"/>
                    {format(day.date, 'EEEE, MMMM d, yyyy')}
                </p>
            </div>
  
            <div className="mt-6 space-y-4">
              {day.activities.length > 0 ? (
                day.activities.map((activity) => (
                    <div key={activity.id} className="ml-0 md:ml-4 p-4 border rounded-lg bg-card shadow-sm">
                      <div className="flex items-start justify-between gap-4">
                          <div className="flex-1">
                              <p className="font-semibold">{activity.name}</p>
                              <p className="text-sm text-muted-foreground mt-1">{activity.description}</p>
                          </div>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground font-mono bg-muted px-2 py-1 rounded-md shrink-0">
                              <Clock className="w-4 h-4" />
                              {activity.time}
                          </div>
                      </div>
                      {activity.location && (
                          <div className="flex items-center gap-2 text-sm text-muted-foreground mt-3 pt-3 border-t">
                              <MapPin className="w-4 h-4 text-primary" />
                              <span>{activity.location}</span>
                          </div>
                      )}
                    </div>
                ))
              ) : (
                <div className="ml-0 md:ml-4 text-center py-8 px-4 border-2 border-dashed rounded-lg">
                  <p className="text-muted-foreground">No activities planned for this day.</p>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    );
}

export default function SharePage() {
  const params = useParams();
  const tripId = typeof params.id === 'string' ? params.id : '';

  const [trip, setTrip] = useState<Trip | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    async function fetchTripData() {
        if (!tripId) {
            setIsLoading(false);
            return;
        }

        try {
            setIsLoading(true);
            const result = await getTrip(tripId);

            if (result) {
                const formattedTrip = {
                    ...result,
                    startDate: new Date(result.startDate),
                    endDate: new Date(result.endDate),
                    itinerary: result.itinerary.map(day => ({
                        ...day,
                        date: new Date(day.date),
                        activities: day.activities.map(activity => ({...activity}))
                    }))
                } as Trip;
                setTrip(formattedTrip);
            } else {
                // This will trigger the notFound() call below
                setTrip(null);
            }
        } catch (err) {
            setError(err as Error);
        } finally {
            setIsLoading(false);
        }
    }

    fetchTripData();
  }, [tripId]);
  
  if (isLoading) {
    return (
        <>
            <AppHeader />
            <div className="p-4 md:p-8 space-y-8">
              <Skeleton className="h-12 w-full max-w-lg" />
              <Skeleton className="h-80 w-full" />
              <div className="space-y-12">
                <div className="space-y-4">
                    <Skeleton className="h-8 w-1/4" />
                    <Skeleton className="h-24 w-full" />
                    <Skeleton className="h-24 w-full" />
                </div>
                 <div className="space-y-4">
                    <Skeleton className="h-8 w-1/4" />
                    <Skeleton className="h-24 w-full" />
                </div>
              </div>
            </div>
        </>
      );
  }

  if (error) {
     return (
      <div className="flex flex-col min-h-screen items-center justify-center">
        <Alert variant="destructive" className="max-w-lg">
            <Terminal className="h-4 w-4" />
            <AlertTitle>Error Loading Trip</AlertTitle>
            <AlertDescription>
                There was a problem fetching the trip data. Please try again later.
                <p className='text-xs mt-2'>{error.message}</p>
            </AlertDescription>
        </Alert>
      </div>
    );
  }
  
  if (!trip) {
    notFound();
  }

  return (
    <>
      <AppHeader />
      <main className="container mx-auto p-4 md:p-8">
        <Alert className="mb-8 bg-blue-100 border-blue-200">
          <Eye className="h-4 w-4" />
          <AlertTitle>Read-Only Itinerary</AlertTitle>
          <AlertDescription>
            You are viewing a shared trip plan. To make changes, please ask the owner to invite you.
          </AlertDescription>
        </Alert>

        <div className="relative h-64 md:h-80 rounded-2xl overflow-hidden mb-8 shadow-lg">
            <Image
                src={trip.imageUrl}
                alt={trip.destination}
                fill
                className="object-cover"
                data-ai-hint={trip.imageHint}
                priority
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
            <div className="absolute bottom-0 left-0 p-8">
                <h1 className="text-4xl md:text-5xl font-bold font-headline text-white">{trip.destination}</h1>
                <p className="text-lg md:text-xl text-white/90 mt-2">
                    {format(trip.startDate, 'MMMM d, yyyy')} - {format(trip.endDate, 'MMMM d, yyyy')}
                </p>
            </div>
        </div>

        <ReadOnlyItineraryTimeline itinerary={trip.itinerary} />
      </main>
    </>
  );
}
