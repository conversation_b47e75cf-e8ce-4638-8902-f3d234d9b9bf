import { relations } from 'drizzle-orm';
import { integer, sqliteTable, text, primaryKey } from 'drizzle-orm/sqlite-core';

export const trips = sqliteTable('trips', {
  id: text('id').primaryKey(),
  destination: text('destination').notNull(),
  startDate: integer('start_date', { mode: 'timestamp' }).notNull(),
  endDate: integer('end_date', { mode: 'timestamp' }).notNull(),
  imageUrl: text('image_url').notNull(),
  imageHint: text('image_hint').notNull(),
});

export const itineraryDays = sqliteTable('itinerary_days', {
    id: text('id').primaryKey(),
    date: integer('date', { mode: 'timestamp' }).notNull(),
    tripId: text('trip_id').notNull().references(() => trips.id, { onDelete: 'cascade' }),
});

export const activities = sqliteTable('activities', {
    id: text('id').primaryKey(),
    time: text('time').notNull(),
    name: text('name').notNull(),
    description: text('description').notNull(),
    location: text('location'),
    dayId: text('day_id').notNull().references(() => itineraryDays.id, { onDelete: 'cascade' }),
});

export const tripsRelations = relations(trips, ({ many }) => ({
    itinerary: many(itineraryDays),
}));

export const itineraryDaysRelations = relations(itineraryDays, ({ one, many }) => ({
    trip: one(trips, {
        fields: [itineraryDays.tripId],
        references: [trips.id],
    }),
    activities: many(activities),
}));

export const activitiesRelations = relations(activities, ({ one }) => ({
    day: one(itineraryDays, {
        fields: [activities.dayId],
        references: [itineraryDays.id],
    }),
}));
