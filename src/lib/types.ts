export type Activity = {
  id: string;
  time: string;
  name: string;
  description: string;
  location?: string | null;
  dayId: string;
};

export type ItineraryDay = {
  id: string;
  date: Date;
  tripId: string;
  activities: Activity[];
};

export type Trip = {
  id:string;
  destination: string;
  startDate: Date;
  endDate: Date;
  itinerary: ItineraryDay[];
  imageUrl: string;
  imageHint: string;
};
