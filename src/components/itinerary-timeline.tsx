
'use client';

import { useState, useTransition } from 'react';
import { format } from 'date-fns';
import { Clock, MapPin, PlusCircle, CalendarDays, Edit, Trash2 } from 'lucide-react';
import type { Activity, ItineraryDay } from '@/lib/types';
import { Button } from './ui/button';
import { AddActivityDialog } from './add-activity-dialog';
import { EditActivityDialog } from './edit-activity-dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { deleteActivityAction } from '@/app/actions';
import { useToast } from '@/hooks/use-toast';

type ItineraryTimelineProps = {
  tripId: string;
  itinerary: ItineraryDay[];
  onDataChange: () => void;
};

export function ItineraryTimeline({ tripId, itinerary, onDataChange }: ItineraryTimelineProps) {
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedDayId, setSelectedDayId] = useState<string | null>(null);
  const [selectedActivity, setSelectedActivity] = useState<Activity | null>(null);
  const [isDeleting, startDeleteTransition] = useTransition();
  const { toast } = useToast();

  const handleAddActivityClick = (dayId: string) => {
    setSelectedDayId(dayId);
    setAddDialogOpen(true);
  };

  const handleEditActivityClick = (activity: Activity) => {
    setSelectedActivity(activity);
    setEditDialogOpen(true);
  };

  const handleDeleteActivity = (activityId: string) => {
    startDeleteTransition(async () => {
        await deleteActivityAction(tripId, activityId);
        toast({
            title: "Activity Deleted",
            description: "The activity has been removed from your itinerary.",
        });
        onDataChange();
    });
  };
  
  return (
    <div className="space-y-12">
      {itinerary.map((day, dayIndex) => (
        <div key={day.id} className="relative pl-10 md:pl-12">
          {/* Timeline line */}
          <div className="absolute left-4 top-2 h-full w-0.5 bg-border -translate-x-1/2"></div>
          
          {/* Day marker */}
          <div className="absolute left-4 top-2 w-4 h-4 rounded-full bg-primary ring-4 ring-background -translate-x-1/2"></div>
          
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="mb-4 sm:mb-0">
                <h3 className="text-xl font-bold font-headline text-primary">
                    Day {dayIndex + 1}
                </h3>
                <p className="text-muted-foreground flex items-center gap-2">
                    <CalendarDays className="w-4 h-4"/>
                    {format(day.date, 'EEEE, MMMM d, yyyy')}
                </p>
            </div>
            <Button variant="outline" size="sm" onClick={() => handleAddActivityClick(day.id)}>
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Activity
            </Button>
          </div>

          <div className="mt-6 space-y-4">
            {day.activities.length > 0 ? (
              day.activities.map((activity) => (
                    <div key={activity.id} className="ml-0 md:ml-4 p-4 border rounded-lg bg-card shadow-sm group">
                        <div className="flex items-start justify-between gap-4">
                            <div className="flex-1">
                              <p className="font-semibold">{activity.name}</p>
                              <p className="text-sm text-muted-foreground mt-1">{activity.description}</p>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground font-mono bg-muted px-2 py-1 rounded-md shrink-0">
                                <Clock className="w-4 h-4" />
                                {activity.time}
                            </div>
                        </div>
                        {activity.location && (
                            <div className="flex items-center gap-2 text-sm text-muted-foreground mt-3 pt-3 border-t">
                                <MapPin className="w-4 h-4 text-primary" />
                                <span>{activity.location}</span>
                            </div>
                        )}
                        <div className="flex justify-end gap-2 mt-2 -mb-2 -mr-2 opacity-0 group-hover:opacity-100 transition-opacity">
                          <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleEditActivityClick(activity)}>
                              <Edit className="h-4 w-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10">
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                                <AlertDialogHeader>
                                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                    <AlertDialogDescription>
                                    This action cannot be undone. This will permanently delete this activity from your itinerary.
                                    </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                    <AlertDialogAction onClick={() => handleDeleteActivity(activity.id)} disabled={isDeleting} className="bg-destructive hover:bg-destructive/90">
                                        {isDeleting ? 'Deleting...' : 'Delete'}
                                    </AlertDialogAction>
                                </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                      </div>
                    </div>
                ))
            ) : (
              <div className="ml-0 md:ml-4 text-center py-8 px-4 border-2 border-dashed rounded-lg">
                <p className="text-muted-foreground">No activities planned for this day.</p>
              </div>
            )}
          </div>
        </div>
      ))}
      {selectedDayId && (
        <AddActivityDialog
          tripId={tripId}
          dayId={selectedDayId}
          isOpen={addDialogOpen}
          onOpenChange={setAddDialogOpen}
          onActivityAdded={onDataChange}
        />
      )}
      {selectedActivity && (
        <EditActivityDialog
            tripId={tripId}
            activity={selectedActivity}
            isOpen={editDialogOpen}
            onOpenChange={setEditDialogOpen}
            onActivityUpdated={onDataChange}
        />
      )}
    </div>
  );
}
