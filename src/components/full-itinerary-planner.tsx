
'use client';

import { useState, useTransition } from 'react';
import { WandSparkles, Plus, Plane, Hotel, Car, MapPin, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { generateFullItineraryAction, addSuggestedActivitiesToAction } from '@/app/actions';
import { useToast } from '@/hooks/use-toast';
import type { Trip } from '@/lib/types';
import { Input } from './ui/input';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from './ui/accordion';

type FullItineraryPlan = {
    flightSuggestion: string;
    hotelSuggestion: string;
    dailyItineraries: {
        day: number;
        theme: string;
        activities: {
            time: string;
            name: string;
            description: string;
            location?: string;
        }[];
        transportation: string;
    }[];
};

type FullItineraryPlannerProps = {
  trip: Trip;
  onDataChange: () => void;
};

export function FullItineraryPlanner({ trip, onDataChange }: FullItineraryPlannerProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [plan, setPlan] = useState<FullItineraryPlan | null>(null);
  const [interests, setInterests] = useState('');
  const [isAdding, startAddingTransition] = useTransition();
  const { toast } = useToast();

  const handleGeneratePlan = async () => {
    setIsLoading(true);
    setPlan(null);
    
    const tripLength = (trip.endDate.getTime() - trip.startDate.getTime()) / (1000 * 3600 * 24) + 1;

    const result = await generateFullItineraryAction({ 
        location: trip.destination,
        tripLength,
        interests,
    });

    if (result.plan) {
      setPlan(result.plan as FullItineraryPlan);
    } else {
      toast({
        variant: "destructive",
        title: "Error",
        description: result.error,
      });
    }
    setIsLoading(false);
  };

  const handleAddDayToItinerary = (dayIndex: number) => {
    if (!plan) return;

    const dayItinerary = plan.dailyItineraries[dayIndex];
    const correspondingDay = trip.itinerary[dayIndex];

    if (!correspondingDay) {
        toast({ variant: 'destructive', title: 'Error', description: 'Could not find the corresponding day in your trip.' });
        return;
    }

    startAddingTransition(async () => {
        // Map the full itinerary plan to the format expected by addSuggestedActivitiesToAction
        const activitiesToAdd = dayItinerary.activities.map(activity => ({
            name: activity.name,
            description: activity.description,
            time: activity.time,
            location: activity.location
        }));

        await addSuggestedActivitiesToAction(trip.id, correspondingDay.id, activitiesToAdd);
        toast({
            title: `Day ${dayItinerary.day} Added`,
            description: `Activities for "${dayItinerary.theme}" have been added to your itinerary.`,
        });
        onDataChange();
    });
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>End-to-End Itinerary Planner</CardTitle>
        <CardDescription>
          Let AI build a complete trip plan from scratch, including flights, hotels, and daily activities.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
            <Input 
                id="full-interests"
                placeholder="e.g., Hiking, Museums, Nightlife"
                value={interests}
                onChange={(e) => setInterests(e.target.value)}
                disabled={isLoading || isAdding}
            />
            <Button onClick={handleGeneratePlan} disabled={isLoading} className="w-full">
                <WandSparkles className="mr-2 h-4 w-4" />
                {isLoading ? 'Building Your Dream Trip...' : 'Generate Full Itinerary'}
            </Button>
        </div>
        
        {isLoading && (
          <div className="mt-4 space-y-2">
            {[...Array(3)].map((_, i) => (
                <div key={i} className="h-20 bg-muted animate-pulse rounded-md" />
            ))}
          </div>
        )}
        
        {plan && !isLoading && (
          <div className="mt-6 space-y-4">
            <Card>
                <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center gap-2"><Plane /> Flight Suggestion</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-sm text-muted-foreground">{plan.flightSuggestion}</p>
                </CardContent>
            </Card>
            <Card>
                <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center gap-2"><Hotel /> Hotel Suggestion</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-sm text-muted-foreground">{plan.hotelSuggestion}</p>
                </CardContent>
            </Card>
            
            <h3 className="text-xl font-semibold pt-4">Daily Plan</h3>
            <Accordion type="single" collapsible className="w-full">
              {plan.dailyItineraries.map((day, index) => (
                <AccordionItem value={`day-${day.day}`} key={day.day}>
                  <AccordionTrigger className="font-bold text-lg">
                    Day {day.day}: {day.theme}
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-4 p-2">
                        {day.activities.map((activity, activityIndex) => (
                            <div key={activityIndex} className="p-3 border rounded-md bg-background/50">
                                <div className="flex items-start justify-between gap-4">
                                    <div className="flex-1">
                                      <p className="font-semibold text-foreground">{activity.name}</p>
                                      <p className="text-sm text-muted-foreground mt-1">{activity.description}</p>
                                    </div>
                                    <div className="flex items-center gap-2 text-xs text-muted-foreground font-mono bg-muted px-2 py-1 rounded-md shrink-0">
                                        <Clock className="w-3 h-3" />
                                        {activity.time}
                                    </div>
                                </div>
                                {activity.location && (
                                    <div className="flex items-center gap-2 text-sm text-muted-foreground mt-2 pt-2 border-t">
                                        <MapPin className="w-3 h-3 text-primary" />
                                        <span>{activity.location}</span>
                                    </div>
                                )}
                            </div>
                        ))}
                         <div className="mt-4 p-3 border-t">
                            <h4 className="font-semibold flex items-center gap-2 text-md"><Car /> Transportation</h4>
                            <p className="text-sm text-muted-foreground mt-1">{day.transportation}</p>
                        </div>
                        <Button size="sm" className="w-full mt-4" onClick={() => handleAddDayToItinerary(index)} disabled={isAdding}>
                            <Plus className="mr-2 h-4 w-4" />
                            {isAdding ? 'Adding...' : `Add Day ${day.day} to Itinerary`}
                        </Button>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
