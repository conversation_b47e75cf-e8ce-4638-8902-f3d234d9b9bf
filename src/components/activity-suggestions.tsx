
'use client';

import { useState, useTransition } from 'react';
import { Wand<PERSON>parkles, Plus, Info, Check, Clock, MapPin, Trash2, Edit } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { generateActivitySuggestionsAction, addSuggestedActivitiesToAction } from '@/app/actions';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useToast } from '@/hooks/use-toast';
import { ItineraryDay } from '@/lib/types';
import { format } from 'date-fns';
import { Alert, AlertDescription, AlertTitle } from './ui/alert';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';

type ActivitySuggestion = {
    name: string;
    description: string;
    time: string;
    location?: string;
};

type ActivitySuggestionsProps = {
  tripId: string;
  location: string;
  itinerary: ItineraryDay[];
  onDataChange: () => void;
};

export function ActivitySuggestions({ tripId, location, itinerary, onDataChange }: ActivitySuggestionsProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [suggestions, setSuggestions] = useState<ActivitySuggestion[]>([]);
  const [addedSuggestions, setAddedSuggestions] = useState<number[]>([]);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [selectedDayId, setSelectedDayId] = useState<string | undefined>();
  const [interests, setInterests] = useState('');
  const [isAdding, startAddingTransition] = useTransition();
  const { toast } = useToast();

  const handleGenerateSuggestions = async () => {
    if (!selectedDayId) {
        toast({
            variant: "destructive",
            title: "No day selected",
            description: "Please select a day to generate suggestions for.",
        });
        return;
    }

    setIsLoading(true);
    setSuggestions([]);
    setAddedSuggestions([]);
    setEditingIndex(null);
    
    const selectedDay = itinerary.find(day => day.id === selectedDayId);
    if (!selectedDay) return;

    const result = await generateActivitySuggestionsAction({ 
        location,
        interests,
        itinerary: [{
            date: selectedDay.date.toISOString(),
            activities: selectedDay.activities.map(a => ({ name: a.name, description: a.description, time: a.time })),
        }]
    });

    if (result.suggestions) {
      setSuggestions(result.suggestions as ActivitySuggestion[]);
    } else {
      toast({
        variant: "destructive",
        title: "Error",
        description: result.error,
      });
    }
    setIsLoading(false);
  };

  const handleAddSuggestion = (suggestion: ActivitySuggestion, index: number) => {
    if (!selectedDayId) return;

    startAddingTransition(async () => {
        await addSuggestedActivitiesToAction(tripId, selectedDayId, [suggestion]);
        setAddedSuggestions(prev => [...prev, index]);
        toast({
            title: "Activity Added",
            description: `${suggestion.name} has been added to your itinerary.`,
        });
        onDataChange();
    });
  }

  const handleAddAllSuggestions = () => {
    if (!selectedDayId) {
      toast({
        variant: "destructive",
        title: "No day selected",
        description: "Please select a day to add the activities to.",
      });
      return;
    }
    startAddingTransition(async () => {
      const activitiesToAdd = suggestions.filter((_, index) => !addedSuggestions.includes(index));

      if (activitiesToAdd.length === 0) {
        toast({
            title: "All suggestions already added",
            description: "You've already added all the current suggestions.",
        });
        return;
      }

      await addSuggestedActivitiesToAction(tripId, selectedDayId, activitiesToAdd);
      
      toast({
        title: "Itinerary Updated",
        description: `Added ${activitiesToAdd.length} new activities to your plan.`,
      })
      setSuggestions([]);
      setSelectedDayId(undefined);
      onDataChange();
    });
  };

  const handleSuggestionChange = (index: number, field: keyof ActivitySuggestion, value: string) => {
    const newSuggestions = [...suggestions];
    newSuggestions[index] = { ...newSuggestions[index], [field]: value };
    setSuggestions(newSuggestions);
  };

  const handleDeleteSuggestion = (index: number) => {
    const newSuggestions = suggestions.filter((_, i) => i !== index);
    setSuggestions(newSuggestions);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>AI Itinerary Planner</CardTitle>
        <CardDescription>
          Select a day, add interests, and let AI generate personalized activity suggestions.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <Select onValueChange={setSelectedDayId} value={selectedDayId} disabled={isLoading || isAdding}>
                    <SelectTrigger>
                        <SelectValue placeholder="Select a day" />
                    </SelectTrigger>
                    <SelectContent>
                        {itinerary.map((day) => (
                        <SelectItem key={day.id} value={day.id}>
                            {format(day.date, "EEE, MMM d")}
                        </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
                 <div className="space-y-1">
                    <Input 
                        id="interests"
                        placeholder="e.g., Art, Food, Nature"
                        value={interests}
                        onChange={(e) => setInterests(e.target.value)}
                        disabled={isLoading || isAdding}
                    />
                 </div>
            </div>
            <Button onClick={handleGenerateSuggestions} disabled={isLoading || !selectedDayId} className="w-full">
                <WandSparkles className="mr-2 h-4 w-4" />
                {isLoading ? 'Generating...' : 'Generate Suggestions'}
            </Button>
        </div>
        
        {isLoading && (
          <div className="mt-4 space-y-2">
            {[...Array(3)].map((_, i) => (
                <div key={i} className="h-28 bg-muted animate-pulse rounded-md" />
            ))}
          </div>
        )}
        
        {suggestions.length > 0 && !isLoading && (
          <div className="mt-6">
            <Alert className='mb-4'>
                <Info className="h-4 w-4" />
                <AlertTitle>Generated Suggestions</AlertTitle>
                <AlertDescription>
                    Review and edit the suggestions below before adding them to your day.
                </AlertDescription>
            </Alert>

            <ul className="space-y-4">
              {suggestions.map((suggestion, index) => {
                const isAdded = addedSuggestions.includes(index);
                const isEditing = editingIndex === index;
                return (
                    <li
                        key={index}
                        className="p-4 bg-card rounded-lg border text-sm"
                    >
                        {isEditing ? (
                           <div className="space-y-2">
                                <Input 
                                    value={suggestion.name}
                                    onChange={(e) => handleSuggestionChange(index, 'name', e.target.value)}
                                    className="font-bold"
                                    placeholder="Activity Name"
                                />
                                <Textarea 
                                    value={suggestion.description}
                                    onChange={(e) => handleSuggestionChange(index, 'description', e.target.value)}
                                    rows={3}
                                    placeholder="Activity Description"
                                />
                                <div className="flex gap-2">
                                    <Input 
                                        type="time"
                                        value={suggestion.time}
                                        onChange={(e) => handleSuggestionChange(index, 'time', e.target.value)}
                                        className="w-1/2"
                                    />
                                    <Input 
                                        value={suggestion.location || ''}
                                        onChange={(e) => handleSuggestionChange(index, 'location', e.target.value)}
                                        placeholder="Location"
                                        className="w-1/2"
                                    />
                                </div>
                                <Button size="sm" onClick={() => setEditingIndex(null)}>Save</Button>
                           </div>
                        ) : (
                            <>
                                <div className="flex items-start justify-between gap-4">
                                    <div className="flex-1">
                                        <p className="font-bold text-foreground">{suggestion.name}</p>
                                        <p className="text-muted-foreground mt-1">{suggestion.description}</p>
                                    </div>
                                    <div className='flex items-center gap-2'>
                                        <div className='flex items-center gap-1 font-mono text-xs bg-muted border px-2 py-1 rounded-md'>
                                            <Clock className="w-3 h-3" />
                                            <span>{suggestion.time}</span>
                                        </div>
                                    </div>
                                </div>
                                <div className="flex items-center justify-between mt-3 pt-3 border-t">
                                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                        {suggestion.location && <>
                                            <MapPin className="w-4 h-4 text-primary" />
                                            <span>{suggestion.location}</span>
                                        </>}
                                    </div>
                                    <div className="flex items-center gap-1">
                                        <Button variant="ghost" size="icon" className="h-7 w-7" onClick={() => setEditingIndex(index)} disabled={isAdded}>
                                            <Edit className="h-4 w-4" />
                                        </Button>
                                        <Button variant="ghost" size="icon" className="h-7 w-7" onClick={() => handleDeleteSuggestion(index)} disabled={isAdded}>
                                            <Trash2 className="h-4 w-4 text-destructive" />
                                        </Button>
                                        <Button 
                                            size="icon" 
                                            className='h-7 w-7'
                                            variant={isAdded ? "secondary" : "outline"}
                                            onClick={() => handleAddSuggestion(suggestion, index)}
                                            disabled={isAdding || isAdded}
                                            aria-label="Add to itinerary"
                                        >
                                            {isAdded ? <Check className="h-4 w-4" /> : <Plus className="h-4 w-4" />}
                                        </Button>
                                    </div>
                                </div>
                            </>
                        )}
                    </li>
                );
              })}
            </ul>
             <Button onClick={handleAddAllSuggestions} disabled={isAdding || addedSuggestions.length === suggestions.length} className="w-full mt-4">
                <Plus className="mr-2 h-4 w-4" />
                {isAdding ? 'Adding...' : 'Add All to Itinerary'}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
