import Link from 'next/link';
import { <PERSON><PERSON><PERSON> } from 'lucide-react';
import type { ReactNode } from 'react';

export function AppHeader({ children }: { children?: ReactNode }) {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        <Link href="/" className="flex items-center gap-2 text-2xl font-bold text-primary">
          <Sailboat className="h-7 w-7" />
          <span className="font-headline">WanderEase</span>
        </Link>
        {children}
      </div>
    </header>
  );
}
