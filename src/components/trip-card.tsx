import Image from 'next/image';
import { format } from 'date-fns';
import { Calendar } from 'lucide-react';
import type { Trip } from '@/lib/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

type TripCardProps = {
  trip: Trip;
};

export function TripCard({ trip }: TripCardProps) {
  return (
    <Card className="w-full h-full hover:shadow-lg transition-shadow duration-300 flex flex-col cursor-pointer">
      <CardHeader className="p-0">
        <div className="aspect-[4/3] relative">
          <Image
            src={trip.imageUrl}
            alt={trip.destination}
            fill
            className="object-cover rounded-t-lg"
            data-ai-hint={trip.imageHint}
          />
        </div>
      </CardHeader>
      <CardContent className="p-4 flex-1 flex flex-col justify-between">
        <div>
          <CardTitle className="text-xl font-bold font-headline leading-tight mb-1">
            {trip.destination}
          </CardTitle>
          <div className="flex items-center text-sm text-muted-foreground">
            <Calendar className="mr-2 h-4 w-4" />
            <span>
              {format(trip.startDate, 'MMM d')} - {format(trip.endDate, 'MMM d, yyyy')}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
