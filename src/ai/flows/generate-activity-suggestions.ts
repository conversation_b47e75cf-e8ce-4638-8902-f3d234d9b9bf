'use server';

/**
 * @fileOverview Generates activity suggestions based on a trip's location.
 *
 * - generateActivitySuggestions - A function that generates activity suggestions.
 * - GenerateActivitySuggestionsInput - The input type for the generateActivitySuggestions function.
 * - GenerateActivitySuggestionsOutput - The return type for the generateActivitySuggestions function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const ActivitySuggestionSchema = z.object({
    name: z.string().describe('The name of the place or activity.'),
    description: z.string().describe('A short, engaging description of the activity or place to visit.'),
    time: z.string().describe('The recommended time to visit in HH:mm format.'),
    location: z.string().optional().describe('The specific address or area of the activity.'),
});

const GenerateActivitySuggestionsInputSchema = z.object({
  location: z.string().describe('The location of the trip.'),
  interests: z.string().optional().describe('A comma-separated list of user interests (e.g., "Art, Food, Nature").'),
  itinerary: z.array(z.object({
    date: z.string().describe('The date of the itinerary day.'),
    activities: z.array(z.object({
        name: z.string().describe('The name of the activity.'),
        description: z.string().describe('The description of the activity.'),
        time: z.string().describe('The time of the activity.'),
    })).describe('Existing activities for that day to avoid scheduling conflicts and duplicates.')
  })).describe('The existing itinerary for context.')
});

export type GenerateActivitySuggestionsInput = z.infer<
  typeof GenerateActivitySuggestionsInputSchema
>;

const GenerateActivitySuggestionsOutputSchema = z.object({
  suggestions: z.array(ActivitySuggestionSchema).describe('A list of suggested activities for a full day, ordered chronologically.')
});

export type GenerateActivitySuggestionsOutput = z.infer<
  typeof GenerateActivitySuggestionsOutputSchema
>;

export async function generateActivitySuggestions(
  input: GenerateActivitySuggestionsInput
): Promise<GenerateActivitySuggestionsOutput> {
  return generateActivitySuggestionsFlow(input);
}

const prompt = ai.definePrompt({
  name: 'generateActivitySuggestionsPrompt',
  input: {schema: GenerateActivitySuggestionsInputSchema},
  output: {schema: GenerateActivitySuggestionsOutputSchema},
  prompt: `You are a world-class travel expert. Your task is to create a personalized and engaging one-day itinerary for a user visiting {{{location}}}.

**CONTEXT:**
*   **Trip Location:** {{{location}}}
*   **User Interests:** {{#if interests}}{{{interests}}}{{else}}General interest{{/if}}

**EXISTING ITINERARY (CRITICAL: DO NOT SUGGEST THESE ACTIVITIES):**
You MUST review this list of activities the user has already planned for this day. Your suggestions must be completely new and not duplicates or variations of what's already here. Be creative. If famous places are already listed, find hidden gems.

{{#if itinerary.length}}
  {{#each itinerary}}
    {{#if this.activities.length}}
      **Already Planned Activities for {{this.date}}:**
      {{#each this.activities}}
- **{{this.name}}**: {{this.description}}
      {{/each}}
    {{else}}
- No activities planned yet.
    {{/if}}
  {{/each}}
{{else}}
- No activities planned yet.
{{/if}}

**YOUR INSTRUCTIONS:**
1.  **GENERATE NEW, UNIQUE SUGGESTIONS:** Based on the user's interests, generate 3-5 NEW activities. If the most famous places are already in the itinerary, suggest hidden gems, local experiences, or alternative attractions.
2.  **LOGICAL FLOW:** The activities must be ordered chronologically and make sense for a day's travel.
3.  **DETAILED & ENGAGING FORMAT:** For each and every suggestion, you must provide:
    *   name: The name of the place or activity (e.g., "Kapaleeshwarar Temple").
    *   description: A compelling, single-paragraph summary of the activity.
    *   time: The optimal time to go, in HH:mm format.
    *   location: The address or general area.

Your goal is to be a hyper-competent assistant, providing fresh, valuable ideas that complement the user's existing plans.`,
});

const generateActivitySuggestionsFlow = ai.defineFlow(
  {
    name: 'generateActivitySuggestionsFlow',
    inputSchema: GenerateActivitySuggestionsInputSchema,
    outputSchema: GenerateActivitySuggestionsOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
