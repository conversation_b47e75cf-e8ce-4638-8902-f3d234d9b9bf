'use server';

/**
 * @fileOverview Generates a complete, end-to-end travel itinerary.
 *
 * - generateFullItinerary - A function that generates a comprehensive travel plan.
 * - GenerateFullItineraryInput - The input type for the generateFullItinerary function.
 * - GenerateFullItineraryOutput - The return type for the generateFullItinerary function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const DailyItinerarySchema = z.object({
    day: z.number().describe('The day number (e.g., 1, 2, 3).'),
    theme: z.string().describe('A creative theme for the day (e.g., "Historical Heart & Culinary Delights").'),
    activities: z.array(z.object({
        time: z.string().describe('The suggested time for the activity in HH:mm format.'),
        name: z.string().describe('The name of the place or activity.'),
        description: z.string().describe('A detailed description of the activity.'),
        location: z.string().optional().describe('The location or address of the activity.'),
    })).describe('A list of activities for the day, ordered chronologically.'),
    transportation: z.string().describe('Recommendations for local transport for the day (e.g., "Use the metro for long distances, but the historical center is best explored on foot.").'),
});

const GenerateFullItineraryInputSchema = z.object({
    location: z.string().describe('The destination of the trip (e.g., "Paris, France").'),
    tripLength: z.number().describe('The total number of days for the trip.'),
    interests: z.string().optional().describe('A comma-separated list of user interests to tailor the plan (e.g., "Art, Food, History, Nature").'),
});

export type GenerateFullItineraryInput = z.infer<
  typeof GenerateFullItineraryInputSchema
>;

const GenerateFullItineraryOutputSchema = z.object({
    flightSuggestion: z.string().describe('A brief, helpful suggestion for finding flights (e.g., "Consider flying into Charles de Gaulle Airport (CDG). Check providers like Google Flights or Skyscanner for the best deals about 2-3 months in advance.").'),
    hotelSuggestion: z.string().describe('A suggestion for accommodation, including area and type (e.g., "For a central location with access to your interests, look for hotels in the Le Marais or Saint-Germain-des-Prés districts. Boutique hotels will offer a charming local experience.").'),
    dailyItineraries: z.array(DailyItinerarySchema).describe('A day-by-day itinerary for the entire trip.'),
});

export type GenerateFullItineraryOutput = z.infer<
  typeof GenerateFullItineraryOutputSchema
>;

export async function generateFullItinerary(
  input: GenerateFullItineraryInput
): Promise<GenerateFullItineraryOutput> {
  return generateFullItineraryFlow(input);
}

const prompt = ai.definePrompt({
  name: 'generateFullItineraryPrompt',
  input: {schema: GenerateFullItineraryInputSchema},
  output: {schema: GenerateFullItineraryOutputSchema},
  prompt: `You are an expert travel agent tasked with creating a complete, end-to-end itinerary for a user. The plan must be highly personalized, practical, and inspiring.

**TRIP DETAILS:**
*   **Destination:** {{{location}}}
*   **Trip Length:** {{{tripLength}}} days
*   **User Interests:** {{#if interests}}{{{interests}}}{{else}}General interest{{/if}}

**YOUR TASK:**
Create a comprehensive travel plan that includes flight advice, hotel recommendations, and a detailed day-by-day itinerary.

**CRITICAL INSTRUCTIONS:**

1.  **Flight and Hotel Suggestions:**
    *   **Flights:** Provide a practical suggestion for which airport to fly into and tips for booking. Do not invent flight numbers or prices.
    *   **Hotels:** Recommend specific neighborhoods or types of accommodation that align with the user's interests. Explain *why* these are good choices.

2.  **Day-by-Day Itinerary (For all {{{tripLength}}} days):**
    *   **Daily Theme:** For each day, create a fun, descriptive theme (e.g., "Coastal Exploration & Local Flavors").
    *   **Activities:** Generate 3-5 distinct, chronologically ordered activities for each day. The activities must be a mix of popular attractions and unique local experiences tailored to the user's interests.
    *   **Activity Details:** For each activity, provide:
        *   time: A logical time in HH:mm format.
        *   name: The name of the place or activity.
        *   description: An engaging description of what to do.
        *   location: The specific location or neighborhood.
    *   **Transportation:** For each day, provide a summary of the best way to get around (e.g., "Utilize the city's tram system," "A rental car is recommended for today's mountain excursion," "This area is best explored on foot.").

Your final output must be a complete, ready-to-use travel plan that is both practical and suited to the user.`,
});

const generateFullItineraryFlow = ai.defineFlow(
  {
    name: 'generateFullItineraryFlow',
    inputSchema: GenerateFullItineraryInputSchema,
    outputSchema: GenerateFullItineraryOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
