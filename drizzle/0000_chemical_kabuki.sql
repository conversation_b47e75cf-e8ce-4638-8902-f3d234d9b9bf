CREATE TABLE `activities` (
	`id` text PRIMARY KEY NOT NULL,
	`time` text NOT NULL,
	`name` text NOT NULL,
	`description` text NOT NULL,
	`location` text,
	`day_id` text NOT NULL,
	FOREIGN KEY (`day_id`) REFERENCES `itinerary_days`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `itinerary_days` (
	`id` text PRIMARY KEY NOT NULL,
	`date` integer NOT NULL,
	`trip_id` text NOT NULL,
	FOREIGN KEY (`trip_id`) REFERENCES `trips`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `trips` (
	`id` text PRIMARY KEY NOT NULL,
	`destination` text NOT NULL,
	`start_date` integer NOT NULL,
	`end_date` integer NOT NULL,
	`image_url` text NOT NULL,
	`image_hint` text NOT NULL
);
